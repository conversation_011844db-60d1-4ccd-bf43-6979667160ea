{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "antd": "^5.26.3", "axios": "^1.8.4", "bootstrap": "^5.3.5", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "jwt-decode": "^4.0.0", "react": "^19.0.0", "react-bootstrap": "^2.10.9", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-router-dom": "^7.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "vite": "^6.1.0"}}